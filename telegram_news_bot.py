#!/usr/bin/env python3
"""
Telegram财经新闻机器人 - 独立监听模块

监听 financial_news_ai.json 文件变化，筛选带有指定标签的消息并发送到Telegram频道。

使用方法:
1. 安装依赖: pip install python-telegram-bot watchdog
2. 配置BOT_TOKEN和CHANNEL_ID
3. 设置目标标签
4. 运行: python telegram_news_bot.py

功能特性:
- 实时监听AI处理结果
- 标签过滤
- 自动发送到频道
- 重复消息检测
- 错误处理和重试
"""

import json
import time
import logging
import asyncio
import os
from datetime import datetime
from typing import Dict, List, Set, Optional
from pathlib import Path

# 尝试导入依赖
try:
    from telegram import Bot
    from telegram.error import TelegramError, RetryAfter, TimedOut
except ImportError:
    print("错误: 缺少 python-telegram-bot 库")
    print("请运行: pip install python-telegram-bot")
    exit(1)

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
except ImportError:
    print("错误: 缺少 watchdog 库")
    print("请运行: pip install watchdog")
    exit(1)

# ==================== 配置部分 ====================

# 尝试导入配置文件
try:
    from telegram_config import *
    print("✅ 已加载配置文件 telegram_config.py")
except ImportError:
    print("⚠️ 未找到配置文件，使用默认配置")
    # 默认配置
    BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"
    CHANNEL_ID = "@your_channel"
    TARGET_TAGS = ["特朗普", "美联储", "美国", "中国", "利率", "股市"]
    SEND_DELAY = 2
    MAX_MESSAGE_LENGTH = 4000
    RETRY_ATTEMPTS = 3
    MESSAGE_TEMPLATE = """📈 **财经快讯**

{content}

🏷️ **相关标签**: {tags}
⏰ **时间**: {time}"""
    USE_MARKDOWN = True
    LOG_LEVEL = 'INFO'

# 文件路径
AI_NEWS_FILE = Path("data/financial_news_ai.json")
SENT_MESSAGES_FILE = Path("data/sent_telegram_messages.json")
LOG_FILE = Path("telegram_bot.log")

# 配置日志
def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

# ==================== Telegram Bot类 ====================

class TelegramNewsBot:
    """Telegram财经新闻机器人"""
    
    def __init__(self, bot_token: str, channel_id: str):
        """
        初始化机器人
        
        Args:
            bot_token: Telegram Bot Token
            channel_id: 目标频道ID
        """
        self.bot_token = bot_token
        self.channel_id = channel_id
        self.bot = Bot(token=bot_token)
        self.sent_messages = self._load_sent_messages()
        
    def _load_sent_messages(self) -> Set[str]:
        """加载已发送消息的记录"""
        try:
            if SENT_MESSAGES_FILE.exists():
                with open(SENT_MESSAGES_FILE, 'r', encoding='utf-8') as f:
                    return set(json.load(f))
        except Exception as e:
            logger.warning(f"加载已发送消息记录失败: {e}")
        return set()
    
    def _save_sent_messages(self):
        """保存已发送消息的记录"""
        try:
            with open(SENT_MESSAGES_FILE, 'w', encoding='utf-8') as f:
                json.dump(list(self.sent_messages), f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存已发送消息记录失败: {e}")
    
    def _contains_target_tags(self, content: str) -> List[str]:
        """
        检查内容是否包含目标标签并满足发送条件

        发送条件:
        1. 同时包含 "重要" 和 "加密货币相关" 标签
        2. 或者包含 "特朗普" 标签
        3. 或者包含 "马斯克" 标签

        Args:
            content: 消息内容

        Returns:
            匹配的标签列表 (如果满足发送条件)
        """
        found_tags = []
        for tag in TARGET_TAGS:
            if f"#{tag}" in content:
                found_tags.append(tag)

        # 检查发送条件
        has_important = "重要" in found_tags
        has_crypto = "加密货币相关" in found_tags
        has_trump = "特朗普" in found_tags
        has_musk = "马斯克" in found_tags

        # 满足发送条件才返回标签
        if (has_important and has_crypto) or has_trump or has_musk:
            return found_tags
        else:
            return []  # 不满足条件，返回空列表
    
    def _format_message(self, ai_content: str, timestamp: str, found_tags: List[str]) -> str:
        """
        格式化Telegram消息 - 简洁格式，直接显示AI内容

        Args:
            ai_content: AI生成的内容
            timestamp: 时间戳 (未使用，保持接口兼容)
            found_tags: 匹配的标签 (未使用，保持接口兼容)

        Returns:
            格式化后的消息
        """
        # 解析时间戳
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            time_str = timestamp

        # 使用配置的消息模板 - 内容 + 分隔线 + 时间
        message = MESSAGE_TEMPLATE.format(content=ai_content, time=time_str)

        # 检查消息长度
        if len(message) > MAX_MESSAGE_LENGTH:
            # 截断内容但保留时间格式
            content_limit = MAX_MESSAGE_LENGTH - 50  # 为分隔线和时间预留空间
            truncated_content = ai_content[:content_limit] + "..."
            message = MESSAGE_TEMPLATE.format(content=truncated_content, time=time_str)

        return message
    
    async def send_message(self, message: str) -> bool:
        """
        发送消息到Telegram频道
        
        Args:
            message: 要发送的消息
            
        Returns:
            是否发送成功
        """
        for attempt in range(RETRY_ATTEMPTS):
            try:
                parse_mode = 'Markdown' if USE_MARKDOWN else None

                # 创建带有更长超时时间的Bot实例
                import telegram
                bot_with_timeout = telegram.Bot(
                    token=self.bot.token,
                    request=telegram.request.HTTPXRequest(
                        connection_pool_size=1,
                        read_timeout=30,
                        write_timeout=30,
                        connect_timeout=30
                    )
                )

                await bot_with_timeout.send_message(
                    chat_id=self.channel_id,
                    text=message,
                    parse_mode=parse_mode
                )
                logger.info(f"✅ 消息发送成功到频道 {self.channel_id}")
                return True

            except RetryAfter as e:
                wait_time = e.retry_after + 1
                logger.warning(f"⏳ 触发速率限制，等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time)

            except TimedOut:
                wait_time = 10 + (attempt * 5)  # 递增等待时间
                logger.warning(f"⏰ 发送超时，重试 {attempt + 1}/{RETRY_ATTEMPTS}，等待 {wait_time} 秒...")
                await asyncio.sleep(wait_time)

            except TelegramError as e:
                logger.error(f"❌ Telegram错误: {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    await asyncio.sleep(5)
                else:
                    return False

            except Exception as e:
                logger.error(f"❌ 发送消息时发生未知错误: {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    await asyncio.sleep(5)
                else:
                    return False
        
        return False
    
    async def process_ai_message(self, ai_record: Dict) -> bool:
        """
        处理AI消息记录
        
        Args:
            ai_record: AI消息记录
            
        Returns:
            是否成功处理
        """
        try:
            content = ai_record.get('content', '')
            timestamp = ai_record.get('timestamp', '')
            
            if not content:
                logger.warning("⚠️ AI记录内容为空，跳过")
                return False
            
            # 生成消息唯一标识
            message_id = f"{timestamp}_{hash(content) % 1000000}"
            
            # 检查是否已发送
            if message_id in self.sent_messages:
                logger.debug(f"📝 消息已发送过，跳过: {message_id}")
                return False
            
            # 检查是否包含目标标签并满足发送条件
            found_tags = self._contains_target_tags(content)
            if not found_tags:
                logger.info(f"🏷️ 消息不满足发送条件，跳过")
                logger.debug(f"📄 内容: {content[:50]}...")
                return False

            logger.info(f"🎯 发现满足发送条件的消息，包含标签: {found_tags}")
            
            # 格式化消息
            formatted_message = self._format_message(content, timestamp, found_tags)
            
            # 发送消息
            success = await self.send_message(formatted_message)
            
            if success:
                # 记录已发送
                self.sent_messages.add(message_id)
                self._save_sent_messages()
                logger.info(f"✅ 消息处理完成: {message_id}")
                return True
            else:
                logger.error(f"❌ 消息发送失败: {message_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 处理AI消息时发生错误: {e}")
            return False


# ==================== 文件监控类 ====================

class AINewsWatcher(FileSystemEventHandler):
    """AI新闻文件监控器"""

    def __init__(self, bot: TelegramNewsBot):
        self.bot = bot
        self.last_modified = 0
        self.last_processed_count = 0
        self.startup_baseline_set = False  # 标记是否已设置启动基线
        
    def on_modified(self, event):
        """处理文件修改事件"""
        if event.is_directory:
            return
            
        if event.src_path.endswith('financial_news_ai.json'):
            # 避免重复处理
            current_time = time.time()
            if current_time - self.last_modified < 2:
                return
            
            self.last_modified = current_time
            logger.info("🔔 检测到AI新闻文件更新！")
            logger.info(f"📁 监听文件: {AI_NEWS_FILE.absolute()}")

            # 异步处理新消息
            try:
                loop = asyncio.get_running_loop()
                loop.create_task(self._process_new_messages())
            except RuntimeError:
                # 如果没有运行的事件循环，创建一个新的
                asyncio.run(self._process_new_messages())

    def set_startup_baseline(self):
        """设置启动时的历史基线 - 将当前所有消息标记为历史"""
        try:
            if not AI_NEWS_FILE.exists():
                logger.info("📋 AI新闻文件不存在，无需设置基线")
                return

            with open(AI_NEWS_FILE, 'r', encoding='utf-8') as f:
                ai_records = json.load(f)

            if not isinstance(ai_records, list) or not ai_records:
                logger.info("📋 AI新闻文件为空，无需设置基线")
                return

            # 设置基线为当前文件中的消息总数
            self.last_processed_count = len(ai_records)
            self.startup_baseline_set = True

            # 获取最后一条消息的时间戳用于日志
            last_record = ai_records[-1]
            last_timestamp = last_record.get('timestamp', 'Unknown')

            logger.info(f"📋 设置启动基线: 共 {self.last_processed_count} 条历史消息")
            logger.info(f"📅 最后一条历史消息时间: {last_timestamp}")
            logger.info("🔄 只会发送此时间点之后的新消息")

        except Exception as e:
            logger.error(f"❌ 设置启动基线时发生错误: {e}")
            self.last_processed_count = 0
            self.startup_baseline_set = True
    
    async def _process_new_messages(self):
        """处理新消息"""
        try:
            # 加载AI新闻文件
            if not AI_NEWS_FILE.exists():
                logger.warning("⚠️ AI新闻文件不存在")
                return

            with open(AI_NEWS_FILE, 'r', encoding='utf-8') as f:
                ai_records = json.load(f)

            if not isinstance(ai_records, list):
                logger.warning("⚠️ AI新闻文件格式错误")
                return

            # 如果是首次启动且未设置基线，则设置基线
            if not self.startup_baseline_set:
                # 记录启动时的消息数量作为历史基线
                self.last_processed_count = len(ai_records)
                self.startup_baseline_set = True

                # 记录启动时间
                startup_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                if ai_records:
                    last_timestamp = ai_records[-1].get('timestamp', 'Unknown')
                    logger.info(f"📋 启动时设置历史基线: 共 {self.last_processed_count} 条历史消息")
                    logger.info(f"📅 启动时间: {startup_time}")
                    logger.info(f"📅 最后一条历史消息时间: {last_timestamp}")
                    logger.info("🔄 只会发送启动后新增的消息")
                else:
                    logger.info(f"📋 启动时设置历史基线: 无历史消息")
                    logger.info(f"📅 启动时间: {startup_time}")
                return

            # 只处理新增的记录
            new_records = ai_records[self.last_processed_count:]
            if not new_records:
                logger.info("ℹ️ 没有新的AI记录需要处理")
                return
            
            logger.info(f"📥 发现 {len(new_records)} 条新的AI记录")

            # 处理每条新记录
            sent_count = 0
            for i, record in enumerate(new_records, 1):
                content = record.get('content', '')
                timestamp = record.get('timestamp', '')

                # 显示监听到的内容
                logger.info(f"📝 监听到第 {i} 条内容:")
                logger.info(f"⏰ 时间戳: {timestamp}")
                logger.info(f"📄 内容: {content[:100]}{'...' if len(content) > 100 else ''}")

                success = await self.bot.process_ai_message(record)
                if success:
                    sent_count += 1
                    logger.info(f"✅ 第 {i} 条消息已发送到频道")
                    # 添加发送间隔
                    await asyncio.sleep(SEND_DELAY)
                else:
                    logger.info(f"⏭️ 第 {i} 条消息未发送 (不包含目标标签或已发送过)")
            
            # 更新已处理计数
            self.last_processed_count = len(ai_records)
            
            logger.info(f"📊 处理完成: 共 {len(new_records)} 条记录，成功发送 {sent_count} 条消息")
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析错误: {e}")
        except Exception as e:
            logger.error(f"❌ 处理新消息时发生错误: {e}")


# ==================== 主函数 ====================

async def test_bot_config():
    """测试机器人配置"""
    print("🧪 测试Telegram机器人配置...")

    # 检查配置
    if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        print("❌ 请先在 telegram_config.py 中配置 BOT_TOKEN")
        return False

    if CHANNEL_ID == "@your_channel":
        print("❌ 请先在 telegram_config.py 中配置 CHANNEL_ID")
        return False

    try:
        # 测试机器人连接
        bot = Bot(token=BOT_TOKEN)
        bot_info = await bot.get_me()
        print(f"✅ 机器人连接成功: @{bot_info.username}")

        # 测试发送消息
        test_message = "🧪 这是一条测试消息，用于验证机器人配置。"
        await bot.send_message(chat_id=CHANNEL_ID, text=test_message)
        print(f"✅ 测试消息发送成功到频道: {CHANNEL_ID}")

        return True

    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

async def main():
    """主函数"""
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        success = await test_bot_config()
        sys.exit(0 if success else 1)

    logger.info("🤖 启动Telegram财经新闻机器人...")

    # 检查配置
    if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        logger.error("❌ 请先配置BOT_TOKEN")
        logger.info("💡 提示: 编辑 telegram_config.py 文件设置您的配置")
        return

    if CHANNEL_ID == "@your_channel":
        logger.error("❌ 请先配置CHANNEL_ID")
        logger.info("💡 提示: 编辑 telegram_config.py 文件设置您的配置")
        return
    
    try:
        # 初始化机器人
        bot = TelegramNewsBot(BOT_TOKEN, CHANNEL_ID)
        logger.info(f"✅ 机器人初始化成功，目标频道: {CHANNEL_ID}")
        
        # 测试机器人连接
        bot_info = await bot.bot.get_me()
        logger.info(f"🤖 机器人信息: @{bot_info.username}")
        
        # 初始化文件监控
        watcher = AINewsWatcher(bot)

        # 设置启动基线 - 将现有消息标记为历史，不发送
        logger.info("📋 设置启动历史基线...")
        if AI_NEWS_FILE.exists():
            await watcher._process_new_messages()  # 这会设置基线而不发送消息
        
        # 开始文件监控
        observer = Observer()
        observer.schedule(watcher, path='.', recursive=False)
        observer.start()
        
        logger.info("🚀 机器人已启动，正在监控AI新闻文件...")
        logger.info(f"📁 监听文件: {AI_NEWS_FILE.absolute()}")
        logger.info(f"🏷️ 目标标签: {', '.join(TARGET_TAGS)}")
        logger.info("� 发送条件:")
        logger.info("   1. 同时包含 '重要' 和 '加密货币相关' 标签")
        logger.info("   2. 或者包含 '特朗普' 标签")
        logger.info("   3. 或者包含 '马斯克' 标签")
        logger.info("�🛑 按 Ctrl+C 停止")
        
        # 保持运行
        try:
            while True:
                await asyncio.sleep(30)
                logger.info(f"💓 机器人运行正常，已发送消息: {len(bot.sent_messages)} 条")
        except KeyboardInterrupt:
            logger.info("🛑 收到停止信号...")
        finally:
            observer.stop()
            observer.join()
            logger.info("👋 机器人已安全退出")
            
    except Exception as e:
        logger.error(f"❌ 机器人启动失败: {e}")


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
