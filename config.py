# -*- coding: utf-8 -*-
"""
多站点财经数据监控器配置文件
包含监控-过滤-输出功能的配置

快速开关控制:
在 MonitorConfig.__init__ 方法中找到 "监听开关配置" 部分
- 设置 'y' 开启监听，'n' 关闭监听
- 例如: JIN10_ENABLED = 'n'  # 关闭金十数据监听
- 例如: SINA_ENABLED = 'y'   # 开启新浪财经监听
"""

import os
from typing import List, Dict
from dataclasses import dataclass

@dataclass
class WebsiteConfig:
    """单个网站配置"""
    name: str
    url: str
    selectors: List[str]
    enabled: bool = True
    check_interval: int = 2  # 检查间隔(秒)

@dataclass
class MonitorConfig:
    """监控配置"""
    page_load_timeout: int = 30
    headless: bool = True
    auto_refresh: bool = True
    refresh_interval: int = 300  # 5分钟自动刷新页面

@dataclass
class FilterConfig:
    """过滤配置"""
    enable_filter: bool = True
    enable_blacklist: bool = True
    enable_whitelist: bool = False
    min_content_length: int = 10
    max_content_length: int = 500
    remove_duplicates: bool = True

@dataclass
class OutputConfig:
    """输出配置"""
    show_timestamp: bool = True
    timestamp_format: str = "%H:%M:%S"
    show_source: bool = True  # 显示消息来源
    save_to_file: bool = False
    log_file_path: str = "financial_news.log"
    max_log_size: int = 10  # MB

class Config:
    """主配置类"""
    
    def __init__(self):
        # ==================== 监听开关配置 ====================
        # 设置为 'y' 开启监听，'n' 关闭监听
        JIN10_ENABLED = 'n'        # 金十数据监听开关
        SINA_ENABLED = 'y'         # 新浪财经监听开关
        SOSOVALUE_ENABLED = 'y'    # SoSoValue监听开关

        # 网站配置列表
        self.websites = [
            # 金十数据
            WebsiteConfig(
                name="金十数据",
                url="https://www.jin10.com/",
                selectors=[
                    ".jin-flash-item",
                    ".flash-item",
                    "[data-flash-id]",
                    ".news-item",
                    ".live-item"
                ],
                enabled=(JIN10_ENABLED.lower() == 'y'),  # y=开启, n=关闭
                check_interval=int(os.getenv('JIN10_CHECK_INTERVAL', '2'))
            ),
            # 新浪财经7x24 - 使用测试验证的有效选择器
            WebsiteConfig(
                name="新浪财经7x24",
                url="https://finance.sina.com.cn/7x24/?tag=0",
                selectors=[
                    "p",  # 段落 - 测试中证明有效，找到21个元素
                    "[data-id]",  # 数据ID选择器 - 测试中找到20个有效元素
                    "[data-time]",  # 时间数据选择器 - 测试中找到20个有效元素
                    "[class*='d_list']",  # 包含d_list的类名 - 测试中找到1个元素
                    "[class*='list']",  # 包含list的类名
                    "li",  # 列表项
                    ".content",  # 内容类
                    "[class*='item']",  # 包含item的类名
                    "article"  # 文章标签
                ],
                enabled=(SINA_ENABLED.lower() == 'y'),  # y=开启, n=关闭
                check_interval=int(os.getenv('SINA_CHECK_INTERVAL', '10'))  # 优化检查间隔
            ),
            # SoSoValue研究
            WebsiteConfig(
                name="SoSoValue研究",
                url="https://sosovalue.com/zh/research",
                selectors=[
                    ".article-item",  # 文章项
                    ".research-item",  # 研究项
                    ".news-item",  # 新闻项
                    "[class*='item']",  # 包含item的类名
                    ".content",  # 内容类
                    "article",  # 文章标签
                    ".title",  # 标题类
                    ".summary",  # 摘要类
                    "h1", "h2", "h3",  # 标题标签
                    "p",  # 段落
                    "li"  # 列表项
                ],
                enabled=(SOSOVALUE_ENABLED.lower() == 'y'),  # y=开启, n=关闭
                check_interval=int(os.getenv('SOSOVALUE_CHECK_INTERVAL', '15'))  # 检查间隔
            )
        ]
        
        # 监控配置
        self.monitor = MonitorConfig(
            page_load_timeout=int(os.getenv('PAGE_TIMEOUT', '30')),
            headless=os.getenv('HEADLESS', 'true').lower() == 'true',
            auto_refresh=os.getenv('AUTO_REFRESH', 'true').lower() == 'true',
            refresh_interval=int(os.getenv('REFRESH_INTERVAL', '300'))
        )
        
        # 过滤配置
        self.filter = FilterConfig(
            enable_filter=os.getenv('ENABLE_FILTER', 'true').lower() == 'true',
            enable_blacklist=os.getenv('ENABLE_BLACKLIST', 'true').lower() == 'true',
            enable_whitelist=os.getenv('ENABLE_WHITELIST', 'false').lower() == 'true',
            min_content_length=int(os.getenv('MIN_LENGTH', '10')),
            max_content_length=int(os.getenv('MAX_LENGTH', '500')),
            remove_duplicates=os.getenv('REMOVE_DUPLICATES', 'true').lower() == 'true'
        )
        
        # 输出配置
        self.output = OutputConfig(
            show_timestamp=os.getenv('SHOW_TIMESTAMP', 'true').lower() == 'true',
            timestamp_format=os.getenv('TIMESTAMP_FORMAT', '%H:%M:%S'),
            show_source=os.getenv('SHOW_SOURCE', 'true').lower() == 'true',
            save_to_file=os.getenv('SAVE_TO_FILE', 'false').lower() == 'true',
            log_file_path=os.getenv('LOG_FILE_PATH', 'financial_news.log'),
            max_log_size=int(os.getenv('MAX_LOG_SIZE', '10'))
        )
        
        # 消息过滤规则(只是移除消息中的指定内容，而不是删除整条消息)
        self.filter_patterns: List[str] = [
            r"\【.*?\】",  # 删除方括号内容
            r"\(来源:.*?\)",  # 删除来源标注
            r"\s+",  # 合并多个空格
            r"^交易时钟",  # 删除开头的交易时钟
            r"北京时间\d{2}:\d{2}",  # 删除北京时间
            r"周[一二三四五六日]\（.*?\）",  # 删除周几标注
            r"\d{2}:\d{2}",  # 删除开头的时间戳(华尔街见闻格式)
        ]
        
        # 黑名单规则(如果消息中包含这些关键词，则删除这条新闻，不进行推送)
        self.blacklist_patterns: List[str] = [
            # "广告",
            # "推广", 
            # "赞助",
            # "交易时钟",
            # "北京时间",
            # "东八区",
            # "现在是",
            # "当前时间",
            # "测试消息",
            # "系统维护",
            # "网站公告",
            # "点击加载更多"
            "查看更多$",  # 匹配以"查看更多"结尾的内容
            "解锁VIP快讯",
            "热点头条.*查看更多$",  # 匹配包含"热点头条"且以"查看更多"结尾的内容
            r":\d{2}\s+热点头条.*查看更多$",  # 匹配时间戳+热点头条+查看更多的格式
        ]
        
        # 白名单规则(只有包含这些关键词的消息才会被推送，启用白名单时生效)
        self.whitelist_patterns: List[str] = [
            # "央行",
            # "美联储", 
            # "加息",
            # "降息",
            # "通胀",
            # "GDP",
            # "就业",
            # "股市",
            # "汇率",
            # "原油",
            # "黄金",
            # "比特币",
            # "经济",
            # "政策",
            # "市场",
            # "财经"
        ]
        
        # 内容替换规则 - 简化和标准化显示内容
        self.replacement_rules: Dict[str, str] = {
            # "美利坚合众国": "美国",
            # "中华人民共和国": "中国", 
            # "联邦储备系统": "美联储",
            # "国内生产总值": "GDP",
            # "消费者价格指数": "CPI",
            # "生产者价格指数": "PPI",
            # "欧洲中央银行": "欧央行",
            # "英格兰银行": "英央行",
            # "日本银行": "日央行"
        }
        
        # 自定义关键词标签 - 根据关键词自动添加相应标签
        self.keyword_tags: Dict[str, List[str]] = {
            # 人物标签
            # "特朗普": ["特朗普", "Trump", "川普"],
            # "拜登": ["拜登", "Biden", "乔·拜登"],
            # "习近平": ["习近平", "主席"],
            # "普京": ["普京", "Putin"],
            # "马斯克": ["马斯克", "Musk", "埃隆"],
            
            # 机构标签
            # "美联储": ["美联储", "Fed", "联邦储备", "鲍威尔"],
            # "欧央行": ["欧央行", "ECB", "欧洲央行", "拉加德"],
            # "日央行": ["日央行", "BOJ", "日本央行"],
            # "中国央行": ["中国央行", "人民银行", "PBOC", "易纲"],
            
            # 国家地区标签
            # "美国": ["美国", "USA", "美利坚"],
            # "中国": ["中国", "China", "中华"],
            # "欧盟": ["欧盟", "EU", "欧洲"],
            # "俄罗斯": ["俄罗斯", "Russia", "俄国"],
            
            # 重要事件标签
            # "突发": ["突发", "紧急", "重要", "警告"],
            # "利率": ["利率", "加息", "降息", "息率"],
            # "通胀": ["通胀", "CPI", "通胀率", "物价"],
            # "GDP": ["GDP", "经济增长", "国内生产总值"],
            
            # 市场标签
            # "股市": ["股市", "股票", "A股", "美股", "港股"],
            # "汇率": ["汇率", "人民币", "美元", "欧元"],
            # "原油": ["原油", "石油", "油价", "WTI"],
            # "黄金": ["黄金", "金价", "贵金属"],
            # "比特币": ["比特币", "BTC", "加密货币", "数字货币"],
        }
        
        # 重要关键词 - 包含这些词的消息会被标记为重要(⭐)
        self.priority_keywords: List[str] = [
            # "突发",
            # "紧急", 
            # "重要",
            # "警告",
            # "通知",
            # "央行决议",
            # "利率决议", 
            # "重大事件",
            # "紧急会议",
            # "特别声明"
        ]
        
        # 时间过滤配置
        self.time_filter: Dict = {
            "enable": False,  # 是否启用时间过滤
            "allowed_hours": list(range(9, 18)),  # 允许的小时范围 9-17点
            "blocked_hours": [],  # 禁止的小时
            "weekend_filter": False  # 是否过滤周末
        }

    def get_enabled_websites(self) -> List[WebsiteConfig]:
        """获取启用的网站配置"""
        return [site for site in self.websites if site.enabled]

    def get_website_by_name(self, name: str) -> WebsiteConfig:
        """根据名称获取网站配置"""
        for site in self.websites:
            if site.name == name:
                return site
        return None

# 全局配置实例
config = Config() 