#!/bin/bash

# 财经新闻监控系统 - 状态检查脚本 (Linux/Mac)

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}"
echo "========================================"
echo "   财经新闻监控系统 - 运行状态"
echo "========================================"
echo -e "${NC}"
echo

# 函数：检查进程状态
check_process() {
    local pid_file=$1
    local process_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $process_name 正在运行 (PID: $pid)${NC}"
            return 0
        else
            echo -e "${RED}❌ $process_name 已停止 (PID文件存在但进程不存在)${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  $process_name 未启动 (无PID文件)${NC}"
        return 1
    fi
}

# 检查各个程序状态
echo -e "${BLUE}📊 程序运行状态：${NC}"
check_process ".monitor.pid" "财经新闻监控器"
check_process ".ai_processor.pid" "AI新闻处理器"
check_process ".telegram_bot.pid" "Telegram机器人"

echo
echo -e "${BLUE}📁 数据文件状态：${NC}"

# 检查数据文件
if [ -f "data/financial_news.json" ]; then
    local count=$(jq length data/financial_news.json 2>/dev/null || echo "无法解析")
    echo -e "${GREEN}✅ 原始新闻数据: data/financial_news.json (记录数: $count)${NC}"
else
    echo -e "${YELLOW}⚠️  原始新闻数据文件不存在${NC}"
fi

if [ -f "data/financial_news_ai.json" ]; then
    local count=$(jq length data/financial_news_ai.json 2>/dev/null || echo "无法解析")
    echo -e "${GREEN}✅ AI处理结果: data/financial_news_ai.json (记录数: $count)${NC}"
else
    echo -e "${YELLOW}⚠️  AI处理结果文件不存在${NC}"
fi

if [ -f "data/processed_news_ids.json" ]; then
    local count=$(jq length data/processed_news_ids.json 2>/dev/null || echo "无法解析")
    echo -e "${GREEN}✅ 已处理ID记录: data/processed_news_ids.json (记录数: $count)${NC}"
else
    echo -e "${YELLOW}⚠️  已处理ID记录文件不存在${NC}"
fi

if [ -f "data/sent_telegram_messages.json" ]; then
    local count=$(jq length data/sent_telegram_messages.json 2>/dev/null || echo "无法解析")
    echo -e "${GREEN}✅ Telegram发送记录: data/sent_telegram_messages.json (记录数: $count)${NC}"
else
    echo -e "${YELLOW}⚠️  Telegram发送记录文件不存在${NC}"
fi

echo
echo -e "${BLUE}📝 日志文件状态：${NC}"

# 检查日志文件
if [ -f "logs/monitor.log" ]; then
    local size=$(du -h logs/monitor.log | cut -f1)
    echo -e "${GREEN}✅ 监控器日志: logs/monitor.log (大小: $size)${NC}"
else
    echo -e "${YELLOW}⚠️  监控器日志文件不存在${NC}"
fi

if [ -f "logs/ai_processor.log" ]; then
    local size=$(du -h logs/ai_processor.log | cut -f1)
    echo -e "${GREEN}✅ AI处理器日志: logs/ai_processor.log (大小: $size)${NC}"
else
    echo -e "${YELLOW}⚠️  AI处理器日志文件不存在${NC}"
fi

if [ -f "logs/telegram_bot.log" ]; then
    local size=$(du -h logs/telegram_bot.log | cut -f1)
    echo -e "${GREEN}✅ Telegram机器人日志: logs/telegram_bot.log (大小: $size)${NC}"
else
    echo -e "${YELLOW}⚠️  Telegram机器人日志文件不存在${NC}"
fi

echo
echo -e "${BLUE}🔧 管理命令：${NC}"
echo "  • 启动所有程序: ./start_all.sh"
echo "  • 停止所有程序: ./stop_all.sh"
echo "  • 查看实时日志: tail -f logs/monitor.log"
echo "  • 查看AI处理日志: tail -f logs/ai_processor.log"
echo "  • 查看Telegram日志: tail -f logs/telegram_bot.log"
echo
