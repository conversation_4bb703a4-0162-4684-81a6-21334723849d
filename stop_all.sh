#!/bin/bash

# 财经新闻监控系统 - 停止所有程序脚本 (Linux/Mac)

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}"
echo "========================================"
echo "   财经新闻监控系统 - 停止所有程序"
echo "========================================"
echo -e "${NC}"
echo

echo -e "${YELLOW}🛑 正在停止所有程序...${NC}"
echo

# 函数：安全停止进程
stop_process() {
    local pid_file=$1
    local process_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            echo -e "${BLUE}停止 $process_name (PID: $pid)...${NC}"
            kill -TERM $pid
            
            # 等待进程优雅退出
            local count=0
            while ps -p $pid > /dev/null 2>&1 && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制杀死
            if ps -p $pid > /dev/null 2>&1; then
                echo -e "${YELLOW}强制停止 $process_name...${NC}"
                kill -KILL $pid
            fi
            
            echo -e "${GREEN}✅ $process_name 已停止${NC}"
        else
            echo -e "${YELLOW}⚠️  $process_name 进程不存在 (PID: $pid)${NC}"
        fi
        rm -f "$pid_file"
    else
        echo -e "${YELLOW}⚠️  未找到 $process_name 的PID文件${NC}"
    fi
}

# 停止各个程序
stop_process ".monitor.pid" "财经新闻监控器"
stop_process ".ai_processor.pid" "AI新闻处理器"
stop_process ".telegram_bot.pid" "Telegram机器人"

echo

# 额外检查：通过进程名停止可能遗漏的进程
echo -e "${BLUE}🔍 检查是否还有相关Python进程运行...${NC}"

# 查找并停止相关Python进程
pkill -f "jin10_web_monitor.py" 2>/dev/null && echo "停止了遗留的jin10_web_monitor.py进程"
pkill -f "financial_news_ai_processor.py" 2>/dev/null && echo "停止了遗留的financial_news_ai_processor.py进程"
pkill -f "telegram_news_bot.py" 2>/dev/null && echo "停止了遗留的telegram_news_bot.py进程"

echo
echo -e "${GREEN}✅ 停止操作完成！${NC}"
echo
echo -e "${CYAN}💡 提示：${NC}"
echo "  • 所有程序已安全停止"
echo "  • 数据文件已安全保存在data文件夹中"
echo "  • 日志文件保存在logs文件夹中"
echo "  • 要重新启动，运行: ./start_all.sh"
echo

# 清理PID文件
rm -f .monitor.pid .ai_processor.pid .telegram_bot.pid

echo -e "${GREEN}🎉 所有程序已安全停止！${NC}"
