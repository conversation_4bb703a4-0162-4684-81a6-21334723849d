#!/bin/bash

# 财经新闻监控系统 - 一键启动脚本 (Linux/Mac)

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}"
echo "========================================"
echo "   财经新闻监控系统 - 一键启动"
echo "========================================"
echo -e "${NC}"
echo
echo "正在启动以下程序："
echo "1. 财经新闻监控器 (jin10_web_monitor.py)"
echo "2. AI新闻处理器 (financial_news_ai_processor.py)"
echo "3. Telegram机器人 (telegram_news_bot.py)"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}❌ 错误：未找到Python，请先安装Python${NC}"
    exit 1
fi

# 确定Python命令
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

echo -e "${BLUE}🐍 使用Python命令: $PYTHON_CMD${NC}"

# 检查data文件夹是否存在
if [ ! -d "data" ]; then
    echo -e "${YELLOW}📁 创建data文件夹...${NC}"
    mkdir -p data
fi

# 检查依赖是否安装
echo -e "${BLUE}📦 检查依赖包...${NC}"
$PYTHON_CMD -c "import selenium, requests, watchdog" 2>/dev/null
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠️  警告：某些依赖包可能未安装${NC}"
    echo -e "${YELLOW}💡 如果程序运行失败，请运行以下命令安装依赖：${NC}"
    echo "   pip install -r requirements.txt"
    echo "   pip install -r requirements_telegram.txt"
    echo
fi

echo
echo -e "${GREEN}🚀 开始启动程序...${NC}"
echo

# 创建日志目录
mkdir -p logs

# 启动财经新闻监控器
echo -e "${BLUE}📊 启动财经新闻监控器...${NC}"
nohup $PYTHON_CMD jin10_web_monitor.py --json-file data/financial_news.json > logs/monitor.log 2>&1 &
MONITOR_PID=$!
echo "财经新闻监控器 PID: $MONITOR_PID"

# 等待2秒
sleep 2

# 启动AI新闻处理器
echo -e "${BLUE}🤖 启动AI新闻处理器...${NC}"
nohup $PYTHON_CMD financial_news_ai_processor.py > logs/ai_processor.log 2>&1 &
AI_PID=$!
echo "AI新闻处理器 PID: $AI_PID"

# 等待2秒
sleep 2

# 启动Telegram机器人
echo -e "${BLUE}📱 启动Telegram机器人...${NC}"
nohup $PYTHON_CMD telegram_news_bot.py > logs/telegram_bot.log 2>&1 &
TELEGRAM_PID=$!
echo "Telegram机器人 PID: $TELEGRAM_PID"

# 保存PID到文件
echo "$MONITOR_PID" > .monitor.pid
echo "$AI_PID" > .ai_processor.pid
echo "$TELEGRAM_PID" > .telegram_bot.pid

echo
echo -e "${GREEN}✅ 所有程序启动完成！${NC}"
echo
echo -e "${CYAN}📋 程序说明：${NC}"
echo "  • 财经新闻监控器：监控金十数据和新浪财经，保存到data/financial_news.json"
echo "  • AI新闻处理器：处理新闻内容，生成摘要和标签，保存到data/financial_news_ai.json"
echo "  • Telegram机器人：监听AI处理结果，发送重要消息到Telegram频道"
echo
echo -e "${CYAN}🔧 管理说明：${NC}"
echo "  • 程序在后台运行"
echo "  • 要停止所有程序，运行: ./stop_all.sh"
echo "  • 要查看运行状态，运行: ./status.sh"
echo "  • 日志文件位置: logs/ 目录"
echo
echo -e "${CYAN}📁 数据文件位置：${NC}"
echo "  • 原始新闻数据：data/financial_news.json"
echo "  • AI处理结果：data/financial_news_ai.json"
echo "  • 已处理ID记录：data/processed_news_ids.json"
echo "  • Telegram发送记录：data/sent_telegram_messages.json"
echo
echo -e "${CYAN}📝 日志文件：${NC}"
echo "  • 监控器日志：logs/monitor.log"
echo "  • AI处理日志：logs/ai_processor.log (同时输出到 financial_news_ai.log)"
echo "  • Telegram机器人日志：logs/telegram_bot.log (同时输出到 telegram_bot.log)"
echo

echo -e "${GREEN}🎉 启动完成！程序正在后台运行...${NC}"
