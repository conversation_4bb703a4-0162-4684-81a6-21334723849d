#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多站点财经数据实时监控器
集成版：包含监控、过滤、标签功能
支持金十数据、华尔街见闻等多个财经网站
"""

import re
import os
import json
import time
import argparse
import threading
from datetime import datetime
from typing import Optional, List, Set, Dict
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from config import config, WebsiteConfig

class NewsJSONStorage:
    """新闻JSON持久化存储类"""
    
    def __init__(self, json_file_path: str = "data/financial_news.json", max_records: int = 10000):
        self.json_file_path = json_file_path
        self.max_records = max_records
        self.lock = threading.Lock()  # 线程锁，确保多线程安全
        
        # 初始化JSON文件
        self._init_json_file()
    
    def _init_json_file(self):
        """初始化JSON文件"""
        try:
            if not os.path.exists(self.json_file_path):
                with open(self.json_file_path, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
                print(f"📁 创建新的JSON存储文件: {self.json_file_path}")
            else:
                print(f"📁 使用现有JSON存储文件: {self.json_file_path}")
        except Exception as e:
            print(f"❌ JSON文件初始化失败: {e}")
    
    def save_news(self, content: str, source: str, timestamp: str = None):
        """保存新闻到JSON文件"""
        if not timestamp:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        news_item = {
            "id": f"{source}_{timestamp}_{hash(content) % 100000}",
            "timestamp": timestamp,
            "source": source,
            "content": content,
            "saved_at": datetime.now().isoformat()
        }
        
        try:
            with self.lock:  # 确保线程安全
                # 读取现有数据
                with open(self.json_file_path, 'r', encoding='utf-8') as f:
                    news_list = json.load(f)
                
                # 添加新闻项
                news_list.append(news_item)
                
                # 如果超过最大记录数，删除最旧的记录
                if len(news_list) > self.max_records:
                    news_list = news_list[-self.max_records:]
                
                # 写回文件
                with open(self.json_file_path, 'w', encoding='utf-8') as f:
                    json.dump(news_list, f, ensure_ascii=False, indent=2)
                
                return True
        except Exception as e:
            print(f"❌ JSON保存失败: {e}")
            return False
    
    def get_news_count(self) -> int:
        """获取已保存的新闻数量"""
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                news_list = json.load(f)
                return len(news_list)
        except:
            return 0
    
    def get_latest_news(self, count: int = 10) -> List[Dict]:
        """获取最新的几条新闻"""
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                news_list = json.load(f)
                return news_list[-count:] if news_list else []
        except:
            return []

class WebsiteMonitor:
    """单个网站监控器"""
    
    def __init__(self, website_config: WebsiteConfig, shared_processed_messages: Set[str], json_storage: NewsJSONStorage, headless=True):
        self.website_config = website_config
        self.headless = headless
        self.driver = None
        self.running = False
        self.config = config
        self.processed_messages = shared_processed_messages  # 共享去重集合
        self.local_previous_contents = set()  # 本站点的历史内容
        self.json_storage = json_storage  # JSON存储实例
        
    def setup_chrome_driver(self):
        """设置Chrome驱动"""
        try:
            # Chrome选项
            options = Options()
            if self.headless:
                options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-web-security')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            # 尝试多种方式创建Chrome驱动
            service = None
            
            # 方法1: 使用webdriver_manager自动下载
            try:
                print(f"🔄 {self.website_config.name} 正在设置Chrome驱动...")
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=options)
                print(f"✅ {self.website_config.name} Chrome驱动设置成功 (自动下载)")
            except Exception as e1:
                print(f"⚠️ {self.website_config.name} 自动下载ChromeDriver失败: {e1}")
                
                # 方法2: 尝试使用系统路径中的chromedriver
                try:
                    self.driver = webdriver.Chrome(options=options)
                    print(f"✅ {self.website_config.name} Chrome驱动设置成功 (系统路径)")
                except Exception as e2:
                    print(f"⚠️ {self.website_config.name} 系统路径ChromeDriver失败: {e2}")
                    
                    # 方法3: 尝试指定Chrome可执行文件路径
                    try:
                        chrome_paths = [
                            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                        ]
                        
                        for chrome_path in chrome_paths:
                            if os.path.exists(chrome_path):
                                options.binary_location = chrome_path
                                break
                        
                        self.driver = webdriver.Chrome(options=options)
                        print(f"✅ {self.website_config.name} Chrome驱动设置成功 (指定路径)")
                    except Exception as e3:
                        print(f"❌ {self.website_config.name} 所有ChromeDriver设置方法都失败:")
                        print(f"   方法1 (自动下载): {e1}")
                        print(f"   方法2 (系统路径): {e2}")
                        print(f"   方法3 (指定路径): {e3}")
                        return False
            
            self.driver.set_page_load_timeout(self.config.monitor.page_load_timeout)
            return True
            
        except Exception as e:
            print(f"❌ {self.website_config.name} Chrome驱动设置失败: {e}")
            return False
    
    def load_website(self):
        """加载网站"""
        try:
            self.driver.get(self.website_config.url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 检查是否成功加载
            try:
                # 使用网站配置的选择器
                for selector in self.website_config.selectors:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        return True
                
                return False
                    
            except NoSuchElementException:
                return False
                
        except Exception as e:
            print(f"❌ {self.website_config.name} 网站加载失败: {e}")
            return False
    
    def extract_news_content(self, element):
        """提取新闻内容"""
        try:
            # 尝试多种方式提取文本
            content = element.text.strip()
            
            if content:
                return content
            
            # 如果没有文本，尝试提取内部元素
            inner_elements = element.find_elements(By.CSS_SELECTOR, "*")
            texts = []
            for inner in inner_elements:
                if inner.text.strip():
                    texts.append(inner.text.strip())
            
            return " ".join(texts) if texts else None
            
        except Exception as e:
            return None
    
    def filter_message(self, message: str) -> Optional[str]:
        """
        对消息进行完整的过滤处理
        """
        if not message or not self.config.filter.enable_filter:
            return message
        
        # 1. 长度检查
        if not self._check_length(message):
            return None
        
        # 2. 时间过滤
        if not self._check_time_filter():
            return None
        
        # 3. 去重检查
        if self.config.filter.remove_duplicates and self._is_duplicate(message):
            return None
        
        # 4. 黑名单检查
        if self.config.filter.enable_blacklist and self._is_blacklisted(message):
            return None
        
        # 5. 白名单检查（如果启用）
        if self.config.filter.enable_whitelist and not self._is_whitelisted(message):
            return None
        
        # 6. 内容清理和替换
        filtered_message = self._clean_content(message)
        filtered_message = self._apply_replacements(filtered_message)
        
        # 7. 添加到已处理消息集合（用于去重）
        if self.config.filter.remove_duplicates:
            self.processed_messages.add(message)
            # 定期清理缓存，避免内存溢出
            if len(self.processed_messages) > 1000:
                self.processed_messages.clear()
        
        return filtered_message.strip() if filtered_message.strip() else None
    
    def _check_length(self, message: str) -> bool:
        """检查消息长度是否符合要求"""
        length = len(message)
        return (self.config.filter.min_content_length <= length <= 
                self.config.filter.max_content_length)
    
    def _check_time_filter(self) -> bool:
        """检查时间过滤设置"""
        if not self.config.time_filter["enable"]:
            return True
        
        now = datetime.now()
        current_hour = now.hour
        
        # 检查小时范围
        if self.config.time_filter["allowed_hours"]:
            if current_hour not in self.config.time_filter["allowed_hours"]:
                return False
        
        if self.config.time_filter["blocked_hours"]:
            if current_hour in self.config.time_filter["blocked_hours"]:
                return False
        
        # 检查周末过滤
        if self.config.time_filter["weekend_filter"]:
            if now.weekday() >= 5:  # 周六=5, 周日=6
                return False
        
        return True
    
    def _is_duplicate(self, message: str) -> bool:
        """检查消息是否重复"""
        return message in self.processed_messages
    
    def _is_blacklisted(self, message: str) -> bool:
        """检查消息是否在黑名单中"""
        for pattern in self.config.blacklist_patterns:
            # 检查是否为正则表达式模式（包含特殊字符）
            if any(char in pattern for char in ['$', '^', '.*', r'\d', '\\', '|', '[', ']', '(', ')', '?', '+', '*']):
                try:
                    # 使用正则表达式匹配
                    if re.search(pattern, message, re.IGNORECASE):
                        return True
                except re.error:
                    # 如果正则表达式有错误，回退到普通字符串匹配
                    if pattern.lower() in message.lower():
                        return True
            else:
                # 普通字符串匹配
                if pattern.lower() in message.lower():
                    return True
        
        return False
    
    def _is_whitelisted(self, message: str) -> bool:
        """检查消息是否在白名单中"""
        if not self.config.whitelist_patterns:
            return True  # 如果白名单为空，则认为所有消息都通过
        
        message_lower = message.lower()
        
        for pattern in self.config.whitelist_patterns:
            if pattern.lower() in message_lower:
                return True
        
        return False
    
    def _clean_content(self, message: str) -> str:
        """清理消息内容"""
        cleaned_message = message
        
        for pattern in self.config.filter_patterns:
            if pattern == r"\s+":
                # 特殊处理：合并多个空格为单个空格
                cleaned_message = re.sub(pattern, " ", cleaned_message)
            else:
                # 删除匹配的内容
                cleaned_message = re.sub(pattern, "", cleaned_message)
        
        return cleaned_message
    
    def _apply_replacements(self, message: str) -> str:
        """应用内容替换规则"""
        replaced_message = message
        
        for find_text, replace_text in self.config.replacement_rules.items():
            replaced_message = replaced_message.replace(find_text, replace_text)
        
        return replaced_message
    
    def is_priority_message(self, message: str) -> bool:
        """检查是否为重要消息"""
        message_lower = message.lower()
        
        for keyword in self.config.priority_keywords:
            if keyword.lower() in message_lower:
                return True
        
        return False
    
    def get_message_tags(self, message: str) -> List[str]:
        """获取消息的标签"""
        tags = []
        message_lower = message.lower()
        
        for tag, keywords in self.config.keyword_tags.items():
            for keyword in keywords:
                if keyword.lower() in message_lower:
                    tags.append(tag)
                    break  # 找到一个匹配就跳出内层循环
        
        return tags
    
    def format_message_output(self, message: str) -> str:
        """格式化消息输出"""
        current_time = datetime.now().strftime(self.config.output.timestamp_format)
        
        # 获取标签
        tags = self.get_message_tags(message)
        
        # 检查是否为重要消息
        is_priority = self.is_priority_message(message)
        
        # 构建输出格式
        output_parts = []
        
        # 添加时间戳
        if self.config.output.show_timestamp:
            output_parts.append(current_time)
        
        # 添加来源
        if self.config.output.show_source:
            output_parts.append(f"[{self.website_config.name}]")
        
        # 添加重要标记
        if is_priority:
            output_parts.append("⭐")
        
        # 添加标签
        if tags:
            tag_str = "[" + "|".join(tags) + "]"
            output_parts.append(tag_str)
        
        # 添加消息内容
        output_parts.append(message)
        
        return " ".join(output_parts)
    
    def monitor_page_changes(self):
        """监控页面变化"""
        while self.running:
            try:
                # 查找新闻元素
                selectors = self.website_config.selectors
                
                current_contents = set()
                found_elements = False
                
                for selector in selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            found_elements = True
                            
                            for element in elements:
                                content = self.extract_news_content(element)
                                if content:
                                    current_contents.add(content)
                            break
                    except Exception as e:
                        continue
                
                if not found_elements:
                    self.driver.refresh()
                    time.sleep(5)
                    continue
                
                # 检查新消息
                new_messages = current_contents - self.local_previous_contents
                
                if new_messages:
                    for message in new_messages:
                        # 应用过滤器
                        filtered_message = self.filter_message(message)
                        
                        if filtered_message:  # 只有通过过滤的消息才输出
                            output = self.format_message_output(filtered_message)
                            print(output)
                            
                            # 保存到文件（如果启用）
                            if self.config.output.save_to_file:
                                self._save_to_file(output)
                            
                            # 保存到JSON（新增功能）
                            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            self.json_storage.save_news(
                                content=filtered_message,
                                source=self.website_config.name,
                                timestamp=current_time
                            )
                
                self.local_previous_contents = current_contents.copy()
                time.sleep(self.website_config.check_interval)
                
            except Exception as e:
                try:
                    self.driver.refresh()
                    time.sleep(5)
                except:
                    break
    
    def run(self):
        """运行监控器"""
        try:
            # 设置Chrome驱动
            if not self.setup_chrome_driver():
                return False
            
            # 加载网站
            if not self.load_website():
                return False
            
            self.running = True
            
            print(f"✅ {self.website_config.name} 监控器已启动")
            
            # 开始监控
            self.monitor_page_changes()
            
        except KeyboardInterrupt:
            pass
        except Exception as e:
            print(f"❌ {self.website_config.name} 监控器运行失败: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止监控器"""
        self.running = False
        
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
        
        print(f"🔴 {self.website_config.name} 监控器已停止")
    
    def _save_to_file(self, content: str):
        """保存内容到文件"""
        try:
            with open(self.config.output.log_file_path, 'a', encoding='utf-8') as f:
                f.write(f"{content}\n")
        except Exception as e:
            pass  # 静默处理文件写入错误


class MultiSiteMonitor:
    """多站点监控器管理器"""
    
    def __init__(self, headless=True, json_file_path="financial_news.json"):
        self.headless = headless
        self.config = config
        self.monitors = []
        self.threads = []
        self.shared_processed_messages = set()  # 全局共享的去重集合
        self.json_storage = NewsJSONStorage(json_file_path)  # JSON存储实例
        
    def create_monitors(self):
        """创建所有启用站点的监控器"""
        enabled_websites = self.config.get_enabled_websites()
        
        for website_config in enabled_websites:
            monitor = WebsiteMonitor(
                website_config, 
                self.shared_processed_messages,
                self.json_storage,
                self.headless
            )
            self.monitors.append(monitor)
        
        print(f"📊 创建了 {len(self.monitors)} 个网站监控器")
        for monitor in self.monitors:
            print(f"  - {monitor.website_config.name}: {monitor.website_config.url}")
        
        # 显示JSON存储状态
        news_count = self.json_storage.get_news_count()
        print(f"💾 JSON存储文件: {self.json_storage.json_file_path} (已存储 {news_count} 条新闻)")
    
    def run(self):
        """运行所有监控器"""
        try:
            # 创建监控器
            self.create_monitors()
            
            if not self.monitors:
                print("❌ 没有启用的网站监控器")
                return
            
            # 为每个监控器创建线程
            for monitor in self.monitors:
                thread = threading.Thread(target=monitor.run, name=f"Monitor-{monitor.website_config.name}")
                thread.daemon = True
                self.threads.append(thread)
            
            # 启动所有线程
            for thread in self.threads:
                thread.start()
            
            print(f"🚀 所有监控器已启动，正在监控 {len(self.monitors)} 个网站...")
            print("按 Ctrl+C 停止监控")
            
            # 等待所有线程
            try:
                while True:
                    time.sleep(1)
                    # 检查线程是否还在运行
                    active_threads = [t for t in self.threads if t.is_alive()]
                    if not active_threads:
                        break
            except KeyboardInterrupt:
                print("\n🛑 收到停止信号...")
                
        except Exception as e:
            print(f"❌ 多站点监控器运行失败: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止所有监控器"""
        print("🔄 正在停止所有监控器...")
        
        # 停止所有监控器
        for monitor in self.monitors:
            monitor.stop()
        
        # 等待所有线程结束
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        print("✅ 所有监控器已停止")


def main():
    parser = argparse.ArgumentParser(description='多站点财经数据实时监控器')
    parser.add_argument('--no-headless', action='store_true', help='显示浏览器窗口')
    parser.add_argument('--save-log', action='store_true', help='保存日志到文件')
    parser.add_argument('--disable-filter', action='store_true', help='禁用过滤器')
    parser.add_argument('--enable-whitelist', action='store_true', help='启用白名单模式')
    parser.add_argument('--sites', nargs='+', help='指定要监控的网站名称', 
                       choices=['金十数据', '新浪财经7x24'], default=None)
    parser.add_argument('--json-file', type=str, default='financial_news.json', 
                       help='指定JSON存储文件路径 (默认: financial_news.json)')
    
    args = parser.parse_args()
    
    # 根据命令行参数调整配置
    if args.save_log:
        config.output.save_to_file = True
    
    if args.disable_filter:
        config.filter.enable_filter = False
        
    if args.enable_whitelist:
        config.filter.enable_whitelist = True
    
    # 如果指定了特定网站，则只启用这些网站
    if args.sites:
        for website in config.websites:
            website.enabled = website.name in args.sites
    
    # 创建并运行多站点监控器
    monitor = MultiSiteMonitor(headless=not args.no_headless, json_file_path=args.json_file)
    monitor.run()

if __name__ == "__main__":
    main() 